#!/usr/bin/env python3
"""
Run comprehensive data source audit for real money trading compliance
"""

import asyncio
import sys
import os
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Import only the auditor to avoid config conflicts
from validation.data_source_auditor import data_source_auditor

async def main():
    """Run comprehensive audit and enforcement"""
    print("🔍 [AUDIT] Starting comprehensive real money trading audit...")
    print("=" * 80)

    try:
        # 1. Set up project root
        project_root = Path(__file__).parent
        print(f"📁 [PROJECT] Auditing project: {project_root}")

        # Initialize auditor with project root
        auditor = data_source_auditor
        auditor.project_root = project_root
        
        # 2. Run data source audit
        print("\n🔍 [AUDIT] Running comprehensive data source audit...")
        audit_result = await auditor.audit_codebase()

        # 3. Generate and display audit report
        print("\n📊 [REPORT] Generating audit report...")
        report = auditor.generate_audit_report(audit_result)
        print(report)

        # 4. Save audit report to file
        report_file = Path("logs/audit") / f"data_source_audit_{audit_result.audit_timestamp.strftime('%Y%m%d_%H%M%S')}.txt"
        report_file.parent.mkdir(parents=True, exist_ok=True)

        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)

        print(f"\n💾 [SAVE] Audit report saved to: {report_file}")

        # 5. Check compliance status
        if audit_result.is_compliant:
            print("\n✅ [SUCCESS] System is COMPLIANT with real money trading requirements")
            print("🚀 [READY] System ready for live trading operations")
        else:
            print(f"\n❌ [FAILURE] System is NOT COMPLIANT - {audit_result.critical_violations + audit_result.high_violations} critical/high violations found")
            print("🛠️ [ACTION] Fix violations before enabling live trading")

            # Show top violations
            print("\n🔧 [FIXES] Top violations to fix:")
            critical_violations = [v for v in audit_result.violations_found if v.severity == 'CRITICAL'][:5]
            for i, violation in enumerate(critical_violations, 1):
                print(f"  {i}. {violation.file_path}:{violation.line_number}")
                print(f"     {violation.description}")
                print(f"     Fix: {violation.suggested_fix}")
                print()
        
        # 6. Display summary
        print("\n" + "=" * 80)
        print("AUDIT SUMMARY")
        print("=" * 80)
        print(f"Files Scanned: {audit_result.total_files_scanned}")
        print(f"Total Violations: {len(audit_result.violations_found)}")
        print(f"Critical: {audit_result.critical_violations}")
        print(f"High: {audit_result.high_violations}")
        print(f"Medium: {audit_result.medium_violations}")
        print(f"Low: {audit_result.low_violations}")
        print(f"Compliance: {'✅ COMPLIANT' if audit_result.is_compliant else '❌ NON-COMPLIANT'}")

        # Return exit code based on compliance
        return 0 if audit_result.is_compliant else 1
        
    except Exception as e:
        print(f"\n❌ [ERROR] Audit failed: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)

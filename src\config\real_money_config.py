#!/usr/bin/env python3
"""
Real Money Trading Configuration System
Manages hardcore trading rules and constraints for real-money trading only
"""

import os
import json
import logging
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from pathlib import Path
from decimal import Decimal
from datetime import datetime

logger = logging.getLogger(__name__)

@dataclass
class TradingConstraints:
    """Trading constraints for real money operations"""
    min_confidence_threshold: float = 0.60  # Minimum 60% confidence
    max_position_size_percent: float = 30.0  # Maximum 30% of balance per trade
    min_balance_usdt: float = 0.90  # Minimum balance for micro-trading
    max_balance_usage_percent: float = 90.0  # Use up to 90% of available balance
    min_usdt_for_buy_mode: float = 10.0  # $10 minimum USDT for BUY orders
    fail_fast_on_insufficient_balance: bool = True
    enable_aggressive_micro_trading: bool = True
    require_real_time_validation: bool = True

@dataclass
class ExchangeSettings:
    """Settings for exchange operations"""
    name: str
    enabled: bool
    priority: int  # 1 = primary, 2 = secondary, etc.
    api_endpoint: str
    websocket_endpoint: str
    max_orders_per_minute: int
    enable_margin_trading: bool = False
    enable_futures_trading: bool = False
    testnet_prohibited: bool = True

@dataclass
class RiskManagement:
    """Risk management settings"""
    max_daily_loss_percent: float = 5.0  # Maximum 5% daily loss
    max_drawdown_percent: float = 10.0  # Maximum 10% drawdown
    stop_loss_percent: float = 2.0  # 2% stop loss
    take_profit_percent: float = 1.0  # 1% take profit
    max_concurrent_trades: int = 5
    enable_circuit_breaker: bool = True
    circuit_breaker_loss_threshold: float = 3.0  # 3% loss triggers circuit breaker

@dataclass
class DataValidation:
    """Data validation requirements"""
    require_live_data_only: bool = True
    max_data_age_seconds: int = 30  # Maximum 30 seconds old data
    min_data_sources: int = 2  # Minimum 2 data sources for validation
    enable_data_source_monitoring: bool = True
    fail_on_stale_data: bool = True
    prohibit_cached_prices: bool = True
    prohibit_fallback_data: bool = True

class RealMoneyTradingConfig:
    """Configuration manager for real money trading"""
    
    def __init__(self, config_file: str = "config/real_money_trading.json"):
        self.config_file = Path(config_file)
        self.config_file.parent.mkdir(parents=True, exist_ok=True)
        
        # Default configuration
        self.trading_constraints = TradingConstraints()
        self.risk_management = RiskManagement()
        self.data_validation = DataValidation()
        self.exchanges: Dict[str, ExchangeSettings] = {}
        self.approved_trading_pairs: List[str] = []  # Empty = dynamic discovery
        self.prohibited_patterns: List[str] = [
            'test', 'demo', 'mock', 'fake', 'simulation', 'sandbox', 'paper'
        ]
        
        # System settings
        self.real_money_mode_enforced = True
        self.allow_degraded_operation = False
        self.config_version = "1.0.0"
        self.last_updated = datetime.now()
        
        # Load existing configuration
        self.load_config()
        
        # Initialize default exchanges
        self._initialize_default_exchanges()

    def _initialize_default_exchanges(self):
        """Initialize default exchange configurations"""
        if not self.exchanges:
            self.exchanges = {
                'bybit': ExchangeSettings(
                    name='bybit',
                    enabled=True,
                    priority=1,
                    api_endpoint='https://api.bybit.com',
                    websocket_endpoint='wss://stream.bybit.com',
                    max_orders_per_minute=60,
                    testnet_prohibited=True
                ),
                'coinbase': ExchangeSettings(
                    name='coinbase',
                    enabled=True,
                    priority=2,
                    api_endpoint='https://api.coinbase.com',
                    websocket_endpoint='wss://ws-feed.exchange.coinbase.com',
                    max_orders_per_minute=10,
                    testnet_prohibited=True
                )
            }

    def load_config(self):
        """Load configuration from file"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r') as f:
                    config_data = json.load(f)
                
                # Load trading constraints
                if 'trading_constraints' in config_data:
                    self.trading_constraints = TradingConstraints(**config_data['trading_constraints'])
                
                # Load risk management
                if 'risk_management' in config_data:
                    self.risk_management = RiskManagement(**config_data['risk_management'])
                
                # Load data validation
                if 'data_validation' in config_data:
                    self.data_validation = DataValidation(**config_data['data_validation'])
                
                # Load exchanges
                if 'exchanges' in config_data:
                    self.exchanges = {}
                    for name, settings in config_data['exchanges'].items():
                        self.exchanges[name] = ExchangeSettings(**settings)
                
                # Load other settings
                self.approved_trading_pairs = config_data.get('approved_trading_pairs', [])
                self.prohibited_patterns = config_data.get('prohibited_patterns', self.prohibited_patterns)
                self.real_money_mode_enforced = config_data.get('real_money_mode_enforced', True)
                self.allow_degraded_operation = config_data.get('allow_degraded_operation', False)
                
                logger.info(f"✅ [CONFIG] Loaded real money trading configuration from {self.config_file}")
            else:
                logger.info(f"📝 [CONFIG] Creating default real money trading configuration")
                self.save_config()
                
        except Exception as e:
            logger.error(f"❌ [CONFIG] Failed to load configuration: {e}")
            logger.info("📝 [CONFIG] Using default configuration")

    def save_config(self):
        """Save configuration to file"""
        try:
            config_data = {
                'config_version': self.config_version,
                'last_updated': self.last_updated.isoformat(),
                'real_money_mode_enforced': self.real_money_mode_enforced,
                'allow_degraded_operation': self.allow_degraded_operation,
                'trading_constraints': asdict(self.trading_constraints),
                'risk_management': asdict(self.risk_management),
                'data_validation': asdict(self.data_validation),
                'exchanges': {name: asdict(settings) for name, settings in self.exchanges.items()},
                'approved_trading_pairs': self.approved_trading_pairs,
                'prohibited_patterns': self.prohibited_patterns
            }
            
            with open(self.config_file, 'w') as f:
                json.dump(config_data, f, indent=2, default=str)
            
            logger.info(f"✅ [CONFIG] Saved real money trading configuration to {self.config_file}")
            
        except Exception as e:
            logger.error(f"❌ [CONFIG] Failed to save configuration: {e}")

    def validate_configuration(self) -> Dict[str, Any]:
        """Validate configuration for real money trading compliance"""
        validation_results = {
            'is_valid': True,
            'errors': [],
            'warnings': [],
            'critical_issues': []
        }
        
        try:
            # Validate trading constraints
            if self.trading_constraints.min_confidence_threshold < 0.60:
                validation_results['critical_issues'].append(
                    f"Confidence threshold too low: {self.trading_constraints.min_confidence_threshold} < 0.60"
                )
                validation_results['is_valid'] = False
            
            if not self.trading_constraints.require_real_time_validation:
                validation_results['critical_issues'].append("Real-time validation is disabled")
                validation_results['is_valid'] = False
            
            if not self.trading_constraints.fail_fast_on_insufficient_balance:
                validation_results['warnings'].append("Fail-fast on insufficient balance is disabled")
            
            # Validate data validation settings
            if not self.data_validation.require_live_data_only:
                validation_results['critical_issues'].append("Live data requirement is disabled")
                validation_results['is_valid'] = False
            
            if self.data_validation.prohibit_fallback_data is False:
                validation_results['critical_issues'].append("Fallback data is allowed")
                validation_results['is_valid'] = False
            
            # Validate exchanges
            enabled_exchanges = [name for name, settings in self.exchanges.items() if settings.enabled]
            if not enabled_exchanges:
                validation_results['critical_issues'].append("No exchanges enabled")
                validation_results['is_valid'] = False
            
            for name, settings in self.exchanges.items():
                if settings.enabled:
                    # Check for test endpoints
                    for prohibited in self.prohibited_patterns:
                        if prohibited in settings.api_endpoint.lower():
                            validation_results['critical_issues'].append(
                                f"Exchange {name} uses prohibited endpoint: {settings.api_endpoint}"
                            )
                            validation_results['is_valid'] = False
            
            # Validate system settings
            if not self.real_money_mode_enforced:
                validation_results['critical_issues'].append("Real money mode enforcement is disabled")
                validation_results['is_valid'] = False
            
            if self.allow_degraded_operation:
                validation_results['warnings'].append("Degraded operation is allowed")
            
            logger.info(f"🔍 [CONFIG] Configuration validation: {'✅ VALID' if validation_results['is_valid'] else '❌ INVALID'}")
            
            if validation_results['critical_issues']:
                for issue in validation_results['critical_issues']:
                    logger.error(f"❌ [CONFIG] Critical issue: {issue}")
            
            if validation_results['warnings']:
                for warning in validation_results['warnings']:
                    logger.warning(f"⚠️ [CONFIG] Warning: {warning}")
            
            return validation_results
            
        except Exception as e:
            logger.error(f"❌ [CONFIG] Configuration validation failed: {e}")
            return {
                'is_valid': False,
                'errors': [str(e)],
                'warnings': [],
                'critical_issues': ['Configuration validation failed']
            }

    def enforce_real_money_mode(self):
        """Enforce real money trading mode"""
        if not self.real_money_mode_enforced:
            raise RuntimeError("Real money mode is not enforced in configuration")
        
        # Set environment variables
        os.environ.update({
            'LIVE_TRADING': 'true',
            'REAL_MONEY_TRADING': 'true',
            'DEMO_MODE': 'false',
            'TEST_MODE': 'false',
            'SIMULATION_MODE': 'false',
            'PAPER_TRADING': 'false',
            'DRY_RUN': 'false'
        })
        
        logger.info("✅ [CONFIG] Real money trading mode enforced")

    def get_trading_constraints(self) -> TradingConstraints:
        """Get trading constraints"""
        return self.trading_constraints

    def get_risk_management(self) -> RiskManagement:
        """Get risk management settings"""
        return self.risk_management

    def get_data_validation(self) -> DataValidation:
        """Get data validation requirements"""
        return self.data_validation

    def get_enabled_exchanges(self) -> Dict[str, ExchangeSettings]:
        """Get enabled exchanges"""
        return {name: settings for name, settings in self.exchanges.items() if settings.enabled}

    def is_trading_pair_approved(self, pair: str) -> bool:
        """Check if trading pair is approved"""
        if not self.approved_trading_pairs:  # Empty list means dynamic discovery
            return True
        return pair in self.approved_trading_pairs

    def update_constraints(self, **kwargs):
        """Update trading constraints"""
        for key, value in kwargs.items():
            if hasattr(self.trading_constraints, key):
                setattr(self.trading_constraints, key, value)
        
        self.last_updated = datetime.now()
        self.save_config()
        logger.info(f"✅ [CONFIG] Updated trading constraints: {kwargs}")

    def update_risk_management(self, **kwargs):
        """Update risk management settings"""
        for key, value in kwargs.items():
            if hasattr(self.risk_management, key):
                setattr(self.risk_management, key, value)
        
        self.last_updated = datetime.now()
        self.save_config()
        logger.info(f"✅ [CONFIG] Updated risk management: {kwargs}")

    def get_configuration_summary(self) -> Dict[str, Any]:
        """Get comprehensive configuration summary"""
        return {
            'config_version': self.config_version,
            'last_updated': self.last_updated.isoformat(),
            'real_money_mode_enforced': self.real_money_mode_enforced,
            'trading_constraints': asdict(self.trading_constraints),
            'risk_management': asdict(self.risk_management),
            'data_validation': asdict(self.data_validation),
            'enabled_exchanges': list(self.get_enabled_exchanges().keys()),
            'approved_trading_pairs_count': len(self.approved_trading_pairs),
            'validation_status': self.validate_configuration()
        }

# Global configuration instance
real_money_config = RealMoneyTradingConfig()
